{"version": 3, "sources": [], "sections": [{"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///root/Github/webMD_write/webmd-writer/src/components/MarkdownEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Editor from '@monaco-editor/react';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport rehypeHighlight from 'rehype-highlight';\nimport { Eye, EyeOff, Split } from 'lucide-react';\n\ninterface MarkdownEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  height?: string;\n}\n\ntype ViewMode = 'edit' | 'preview' | 'split';\n\nexport default function MarkdownEditor({\n  value,\n  onChange,\n  placeholder = '开始撰写你的内容...',\n  height = '500px'\n}: MarkdownEditorProps) {\n  const [viewMode, setViewMode] = useState<ViewMode>('split');\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return (\n      <div className=\"border rounded-lg p-4 bg-gray-50\" style={{ height }}>\n        <div className=\"animate-pulse\">加载编辑器中...</div>\n      </div>\n    );\n  }\n\n  const ViewModeButton = ({ mode, icon: Icon, label }: { \n    mode: ViewMode; \n    icon: any; \n    label: string; \n  }) => (\n    <button\n      onClick={() => setViewMode(mode)}\n      className={`flex items-center gap-2 px-3 py-1.5 rounded text-sm transition-colors ${\n        viewMode === mode\n          ? 'bg-blue-500 text-white'\n          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n      }`}\n      title={label}\n    >\n      <Icon size={16} />\n      <span className=\"hidden sm:inline\">{label}</span>\n    </button>\n  );\n\n  return (\n    <div className=\"border rounded-lg overflow-hidden bg-white\">\n      {/* 工具栏 */}\n      <div className=\"border-b bg-gray-50 p-3 flex items-center justify-between\">\n        <div className=\"flex items-center gap-2\">\n          <ViewModeButton mode=\"edit\" icon={EyeOff} label=\"编辑\" />\n          <ViewModeButton mode=\"preview\" icon={Eye} label=\"预览\" />\n          <ViewModeButton mode=\"split\" icon={Split} label=\"分屏\" />\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          {value.length} 字符\n        </div>\n      </div>\n\n      {/* 编辑器内容 */}\n      <div className=\"flex\" style={{ height }}>\n        {/* 编辑器面板 */}\n        {(viewMode === 'edit' || viewMode === 'split') && (\n          <div className={`${viewMode === 'split' ? 'w-1/2 border-r' : 'w-full'}`}>\n            <Editor\n              height={height}\n              defaultLanguage=\"markdown\"\n              value={value}\n              onChange={(val) => onChange(val || '')}\n              theme=\"vs-light\"\n              options={{\n                minimap: { enabled: false },\n                wordWrap: 'on',\n                lineNumbers: 'on',\n                scrollBeyondLastLine: false,\n                automaticLayout: true,\n                fontSize: 14,\n                fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                placeholder,\n              }}\n            />\n          </div>\n        )}\n\n        {/* 预览面板 */}\n        {(viewMode === 'preview' || viewMode === 'split') && (\n          <div className={`${viewMode === 'split' ? 'w-1/2' : 'w-full'} overflow-auto`}>\n            <div className=\"p-4 prose prose-sm max-w-none\">\n              {value.trim() ? (\n                <ReactMarkdown\n                  remarkPlugins={[remarkGfm]}\n                  rehypePlugins={[rehypeHighlight]}\n                  components={{\n                    // 自定义组件样式\n                    h1: ({ children }) => (\n                      <h1 className=\"text-2xl font-bold mb-4 text-gray-900 border-b pb-2\">\n                        {children}\n                      </h1>\n                    ),\n                    h2: ({ children }) => (\n                      <h2 className=\"text-xl font-semibold mb-3 text-gray-800 mt-6\">\n                        {children}\n                      </h2>\n                    ),\n                    h3: ({ children }) => (\n                      <h3 className=\"text-lg font-medium mb-2 text-gray-800 mt-4\">\n                        {children}\n                      </h3>\n                    ),\n                    p: ({ children }) => (\n                      <p className=\"mb-4 text-gray-700 leading-relaxed\">\n                        {children}\n                      </p>\n                    ),\n                    code: ({ children, className }) => {\n                      const isInline = !className;\n                      return isInline ? (\n                        <code className=\"bg-gray-100 px-1.5 py-0.5 rounded text-sm font-mono text-red-600\">\n                          {children}\n                        </code>\n                      ) : (\n                        <code className={className}>{children}</code>\n                      );\n                    },\n                    pre: ({ children }) => (\n                      <pre className=\"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto mb-4\">\n                        {children}\n                      </pre>\n                    ),\n                    blockquote: ({ children }) => (\n                      <blockquote className=\"border-l-4 border-blue-500 pl-4 py-2 mb-4 bg-blue-50 text-gray-700 italic\">\n                        {children}\n                      </blockquote>\n                    ),\n                    ul: ({ children }) => (\n                      <ul className=\"list-disc list-inside mb-4 space-y-1\">\n                        {children}\n                      </ul>\n                    ),\n                    ol: ({ children }) => (\n                      <ol className=\"list-decimal list-inside mb-4 space-y-1\">\n                        {children}\n                      </ol>\n                    ),\n                    li: ({ children }) => (\n                      <li className=\"text-gray-700\">{children}</li>\n                    ),\n                    table: ({ children }) => (\n                      <div className=\"overflow-x-auto mb-4\">\n                        <table className=\"min-w-full border border-gray-300\">\n                          {children}\n                        </table>\n                      </div>\n                    ),\n                    th: ({ children }) => (\n                      <th className=\"border border-gray-300 px-4 py-2 bg-gray-100 font-semibold text-left\">\n                        {children}\n                      </th>\n                    ),\n                    td: ({ children }) => (\n                      <td className=\"border border-gray-300 px-4 py-2\">\n                        {children}\n                      </td>\n                    ),\n                  }}\n                >\n                  {value}\n                </ReactMarkdown>\n              ) : (\n                <div className=\"text-gray-400 italic\">\n                  在左侧编辑器中输入内容，这里将显示预览效果\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AAkBe,SAAS,eAAe,EACrC,KAAK,EACL,QAAQ,EACR,cAAc,aAAa,EAC3B,SAAS,OAAO,EACI;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;YAAmC,OAAO;gBAAE;YAAO;sBAChE,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAIhD,iBACC,8OAAC;YACC,SAAS,IAAM,YAAY;YAC3B,WAAW,CAAC,sEAAsE,EAChF,aAAa,OACT,2BACA,+CACJ;YACF,OAAO;;8BAEP,8OAAC;oBAAK,MAAM;;;;;;8BACZ,8OAAC;oBAAK,WAAU;8BAAoB;;;;;;;;;;;;IAIxC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAe,MAAK;gCAAO,MAAM,0MAAA,CAAA,SAAM;gCAAE,OAAM;;;;;;0CAChD,8OAAC;gCAAe,MAAK;gCAAU,MAAM,gMAAA,CAAA,MAAG;gCAAE,OAAM;;;;;;0CAChD,8OAAC;gCAAe,MAAK;gCAAQ,MAAM,oMAAA,CAAA,QAAK;gCAAE,OAAM;;;;;;;;;;;;kCAElD,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM;4BAAC;;;;;;;;;;;;;0BAKlB,8OAAC;gBAAI,WAAU;gBAAO,OAAO;oBAAE;gBAAO;;oBAEnC,CAAC,aAAa,UAAU,aAAa,OAAO,mBAC3C,8OAAC;wBAAI,WAAW,GAAG,aAAa,UAAU,mBAAmB,UAAU;kCACrE,cAAA,8OAAC,6KAAA,CAAA,UAAM;4BACL,QAAQ;4BACR,iBAAgB;4BAChB,OAAO;4BACP,UAAU,CAAC,MAAQ,SAAS,OAAO;4BACnC,OAAM;4BACN,SAAS;gCACP,SAAS;oCAAE,SAAS;gCAAM;gCAC1B,UAAU;gCACV,aAAa;gCACb,sBAAsB;gCACtB,iBAAiB;gCACjB,UAAU;gCACV,YAAY;gCACZ;4BACF;;;;;;;;;;;oBAML,CAAC,aAAa,aAAa,aAAa,OAAO,mBAC9C,8OAAC;wBAAI,WAAW,GAAG,aAAa,UAAU,UAAU,SAAS,cAAc,CAAC;kCAC1E,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,mBACT,8OAAC,wLAAA,CAAA,UAAa;gCACZ,eAAe;oCAAC,6IAAA,CAAA,UAAS;iCAAC;gCAC1B,eAAe;oCAAC,mJAAA,CAAA,UAAe;iCAAC;gCAChC,YAAY;oCACV,UAAU;oCACV,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDACX;;;;;;oCAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDACX;;;;;;oCAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDACX;;;;;;oCAGL,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,8OAAC;4CAAE,WAAU;sDACV;;;;;;oCAGL,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;wCAC5B,MAAM,WAAW,CAAC;wCAClB,OAAO,yBACL,8OAAC;4CAAK,WAAU;sDACb;;;;;mEAGH,8OAAC;4CAAK,WAAW;sDAAY;;;;;;oCAEjC;oCACA,KAAK,CAAC,EAAE,QAAQ,EAAE,iBAChB,8OAAC;4CAAI,WAAU;sDACZ;;;;;;oCAGL,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,8OAAC;4CAAW,WAAU;sDACnB;;;;;;oCAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDACX;;;;;;oCAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDACX;;;;;;oCAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDAAiB;;;;;;oCAEjC,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;0DACd;;;;;;;;;;;oCAIP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDACX;;;;;;oCAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;4CAAG,WAAU;sDACX;;;;;;gCAGP;0CAEC;;;;;qDAGH,8OAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///root/Github/webMD_write/webmd-writer/src/lib/github.ts"], "sourcesContent": ["import { Octokit } from '@octokit/rest';\n\nexport interface GitHubConfig {\n  token: string;\n  owner: string;\n  repo: string;\n}\n\nexport interface FileContent {\n  path: string;\n  content: string;\n  message: string;\n  branch?: string;\n}\n\nexport class GitHubService {\n  private octokit: Octokit;\n  private config: GitHubConfig;\n\n  constructor(config: GitHubConfig) {\n    this.config = config;\n    this.octokit = new Octokit({\n      auth: config.token,\n    });\n  }\n\n  /**\n   * 创建或更新文件\n   */\n  async createOrUpdateFile(fileData: FileContent): Promise<any> {\n    const { path, content, message, branch = 'main' } = fileData;\n    \n    try {\n      // 首先尝试获取文件，检查是否存在\n      let sha: string | undefined;\n      try {\n        const { data } = await this.octokit.rest.repos.getContent({\n          owner: this.config.owner,\n          repo: this.config.repo,\n          path,\n          ref: branch,\n        });\n        \n        if ('sha' in data) {\n          sha = data.sha;\n        }\n      } catch (error: any) {\n        // 文件不存在，这是正常的\n        if (error.status !== 404) {\n          throw error;\n        }\n      }\n\n      // 创建或更新文件\n      const response = await this.octokit.rest.repos.createOrUpdateFileContents({\n        owner: this.config.owner,\n        repo: this.config.repo,\n        path,\n        message,\n        content: Buffer.from(content, 'utf-8').toString('base64'),\n        branch,\n        ...(sha && { sha }),\n      });\n\n      return response.data;\n    } catch (error) {\n      console.error('GitHub API Error:', error);\n      throw new Error(`Failed to ${sha ? 'update' : 'create'} file: ${error}`);\n    }\n  }\n\n  /**\n   * 获取文件内容\n   */\n  async getFile(path: string, branch = 'main'): Promise<string> {\n    try {\n      const { data } = await this.octokit.rest.repos.getContent({\n        owner: this.config.owner,\n        repo: this.config.repo,\n        path,\n        ref: branch,\n      });\n\n      if ('content' in data) {\n        return Buffer.from(data.content, 'base64').toString('utf-8');\n      }\n      \n      throw new Error('File content not found');\n    } catch (error) {\n      console.error('GitHub API Error:', error);\n      throw new Error(`Failed to get file: ${error}`);\n    }\n  }\n\n  /**\n   * 列出目录内容\n   */\n  async listDirectory(path: string, branch = 'main'): Promise<any[]> {\n    try {\n      const { data } = await this.octokit.rest.repos.getContent({\n        owner: this.config.owner,\n        repo: this.config.repo,\n        path,\n        ref: branch,\n      });\n\n      if (Array.isArray(data)) {\n        return data;\n      }\n      \n      return [];\n    } catch (error) {\n      console.error('GitHub API Error:', error);\n      throw new Error(`Failed to list directory: ${error}`);\n    }\n  }\n\n  /**\n   * 验证GitHub配置\n   */\n  async validateConfig(): Promise<boolean> {\n    try {\n      await this.octokit.rest.repos.get({\n        owner: this.config.owner,\n        repo: this.config.repo,\n      });\n      return true;\n    } catch (error) {\n      console.error('GitHub validation error:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 生成文件路径\n   */\n  static generateFilePath(type: 'blog' | 'essay', title: string): string {\n    const now = new Date();\n    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD\n    const slug = title\n      .toLowerCase()\n      .replace(/[^a-z0-9\\u4e00-\\u9fa5]/g, '-')\n      .replace(/-+/g, '-')\n      .replace(/^-|-$/g, '');\n    \n    const basePath = type === 'blog' ? 'src/content/blog' : 'src/content/essays';\n    return `${basePath}/${dateStr}-${slug}.md`;\n  }\n\n  /**\n   * 生成Markdown frontmatter\n   */\n  static generateFrontmatter(title: string, type: 'blog' | 'essay', tags: string[] = []): string {\n    const now = new Date();\n    const frontmatter = [\n      '---',\n      `title: \"${title}\"`,\n      `date: ${now.toISOString()}`,\n      `type: ${type}`,\n      ...(tags.length > 0 ? [`tags: [${tags.map(tag => `\"${tag}\"`).join(', ')}]`] : []),\n      'draft: false',\n      '---',\n      '',\n    ];\n    \n    return frontmatter.join('\\n');\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAeO,MAAM;IACH,QAAiB;IACjB,OAAqB;IAE7B,YAAY,MAAoB,CAAE;QAChC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,IAAI,yJAAA,CAAA,UAAO,CAAC;YACzB,MAAM,OAAO,KAAK;QACpB;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,QAAqB,EAAgB;QAC5D,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,MAAM,EAAE,GAAG;QAEpD,IAAI;YACF,kBAAkB;YAClB,IAAI;YACJ,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;oBACxD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;oBACxB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;oBACtB;oBACA,KAAK;gBACP;gBAEA,IAAI,SAAS,MAAM;oBACjB,OAAM,KAAK,GAAG;gBAChB;YACF,EAAE,OAAO,OAAY;gBACnB,cAAc;gBACd,IAAI,MAAM,MAAM,KAAK,KAAK;oBACxB,MAAM;gBACR;YACF;YAEA,UAAU;YACV,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC;gBACxE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;gBACtB;gBACA;gBACA,SAAS,OAAO,IAAI,CAAC,SAAS,SAAS,QAAQ,CAAC;gBAChD;gBACA,GAAI,QAAO;oBAAE,KAAA;gBAAI,CAAC;YACpB;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,MAAM,WAAW,SAAS,OAAO,EAAE,OAAO;QACzE;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,IAAY,EAAE,SAAS,MAAM,EAAmB;QAC5D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACxD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;gBACtB;gBACA,KAAK;YACP;YAEA,IAAI,aAAa,MAAM;gBACrB,OAAO,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE,UAAU,QAAQ,CAAC;YACtD;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,OAAO;QAChD;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,IAAY,EAAE,SAAS,MAAM,EAAkB;QACjE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACxD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;gBACtB;gBACA,KAAK;YACP;YAEA,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,OAAO;YACT;YAEA,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,OAAO;QACtD;IACF;IAEA;;GAEC,GACD,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBAChC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAO,iBAAiB,IAAsB,EAAE,KAAa,EAAU;QACrE,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa;QAC9D,MAAM,OAAO,MACV,WAAW,GACX,OAAO,CAAC,2BAA2B,KACnC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,UAAU;QAErB,MAAM,WAAW,SAAS,SAAS,qBAAqB;QACxD,OAAO,GAAG,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC;IAC5C;IAEA;;GAEC,GACD,OAAO,oBAAoB,KAAa,EAAE,IAAsB,EAAE,OAAiB,EAAE,EAAU;QAC7F,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc;YAClB;YACA,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACnB,CAAC,MAAM,EAAE,IAAI,WAAW,IAAI;YAC5B,CAAC,MAAM,EAAE,MAAM;eACX,KAAK,MAAM,GAAG,IAAI;gBAAC,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;aAAC,GAAG,EAAE;YAChF;YACA;YACA;SACD;QAED,OAAO,YAAY,IAAI,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///root/Github/webMD_write/webmd-writer/src/components/ConfigModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X, Github, AlertCircle, CheckCircle } from 'lucide-react';\nimport { GitHubConfig, GitHubService } from '@/lib/github';\n\ninterface ConfigModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfigSave: (config: GitHubConfig) => void;\n  currentConfig?: GitHubConfig;\n}\n\nexport default function ConfigModal({\n  isOpen,\n  onClose,\n  onConfigSave,\n  currentConfig\n}: ConfigModalProps) {\n  const [config, setConfig] = useState<GitHubConfig>({\n    token: '',\n    owner: '',\n    repo: ''\n  });\n  const [isValidating, setIsValidating] = useState(false);\n  const [validationResult, setValidationResult] = useState<{\n    isValid: boolean;\n    message: string;\n  } | null>(null);\n\n  useEffect(() => {\n    if (currentConfig) {\n      setConfig(currentConfig);\n    }\n  }, [currentConfig]);\n\n  useEffect(() => {\n    if (isOpen) {\n      setValidationResult(null);\n    }\n  }, [isOpen]);\n\n  const handleValidate = async () => {\n    if (!config.token || !config.owner || !config.repo) {\n      setValidationResult({\n        isValid: false,\n        message: '请填写所有必填字段'\n      });\n      return;\n    }\n\n    setIsValidating(true);\n    setValidationResult(null);\n\n    try {\n      const githubService = new GitHubService(config);\n      const isValid = await githubService.validateConfig();\n      \n      setValidationResult({\n        isValid,\n        message: isValid ? '配置验证成功！' : '配置验证失败，请检查Token和仓库信息'\n      });\n    } catch (error) {\n      setValidationResult({\n        isValid: false,\n        message: `验证失败: ${error}`\n      });\n    } finally {\n      setIsValidating(false);\n    }\n  };\n\n  const handleSave = () => {\n    if (validationResult?.isValid) {\n      onConfigSave(config);\n      onClose();\n    } else {\n      handleValidate();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div className=\"flex items-center gap-2\">\n            <Github className=\"text-gray-700\" size={20} />\n            <h2 className=\"text-lg font-semibold text-gray-900\">GitHub 配置</h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* 内容 */}\n        <div className=\"p-6 space-y-4\">\n          {/* GitHub Token */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              GitHub Personal Access Token *\n            </label>\n            <input\n              type=\"password\"\n              value={config.token}\n              onChange={(e) => setConfig({ ...config, token: e.target.value })}\n              placeholder=\"ghp_xxxxxxxxxxxxxxxxxxxx\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">\n              需要 repo 权限的 Personal Access Token\n            </p>\n          </div>\n\n          {/* 仓库所有者 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              仓库所有者 *\n            </label>\n            <input\n              type=\"text\"\n              value={config.owner}\n              onChange={(e) => setConfig({ ...config, owner: e.target.value })}\n              placeholder=\"your-username\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">\n              GitHub 用户名或组织名\n            </p>\n          </div>\n\n          {/* 仓库名称 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              仓库名称 *\n            </label>\n            <input\n              type=\"text\"\n              value={config.repo}\n              onChange={(e) => setConfig({ ...config, repo: e.target.value })}\n              placeholder=\"your-blog-repo\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">\n              博客仓库的名称\n            </p>\n          </div>\n\n          {/* 验证结果 */}\n          {validationResult && (\n            <div className={`flex items-center gap-2 p-3 rounded-md ${\n              validationResult.isValid \n                ? 'bg-green-50 text-green-700 border border-green-200' \n                : 'bg-red-50 text-red-700 border border-red-200'\n            }`}>\n              {validationResult.isValid ? (\n                <CheckCircle size={16} />\n              ) : (\n                <AlertCircle size={16} />\n              )}\n              <span className=\"text-sm\">{validationResult.message}</span>\n            </div>\n          )}\n\n          {/* 说明 */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3\">\n            <h4 className=\"text-sm font-medium text-blue-900 mb-2\">如何获取 GitHub Token：</h4>\n            <ol className=\"text-xs text-blue-800 space-y-1 list-decimal list-inside\">\n              <li>访问 GitHub Settings → Developer settings → Personal access tokens</li>\n              <li>点击 \"Generate new token (classic)\"</li>\n              <li>选择 \"repo\" 权限</li>\n              <li>复制生成的 token</li>\n            </ol>\n          </div>\n        </div>\n\n        {/* 底部按钮 */}\n        <div className=\"flex gap-3 p-6 border-t bg-gray-50\">\n          <button\n            onClick={onClose}\n            className=\"flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\"\n          >\n            取消\n          </button>\n          <button\n            onClick={handleValidate}\n            disabled={isValidating || !config.token || !config.owner || !config.repo}\n            className=\"px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isValidating ? '验证中...' : '验证配置'}\n          </button>\n          <button\n            onClick={handleSave}\n            disabled={!validationResult?.isValid}\n            className=\"flex-1 px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            保存配置\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAae,SAAS,YAAY,EAClC,MAAM,EACN,OAAO,EACP,YAAY,EACZ,aAAa,EACI;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACjD,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG7C;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,UAAU;QACZ;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,IAAI,EAAE;YAClD,oBAAoB;gBAClB,SAAS;gBACT,SAAS;YACX;YACA;QACF;QAEA,gBAAgB;QAChB,oBAAoB;QAEpB,IAAI;YACF,MAAM,gBAAgB,IAAI,oHAAA,CAAA,gBAAa,CAAC;YACxC,MAAM,UAAU,MAAM,cAAc,cAAc;YAElD,oBAAoB;gBAClB;gBACA,SAAS,UAAU,YAAY;YACjC;QACF,EAAE,OAAO,OAAO;YACd,oBAAoB;gBAClB,SAAS;gBACT,SAAS,CAAC,MAAM,EAAE,OAAO;YAC3B;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,kBAAkB,SAAS;YAC7B,aAAa;YACb;QACF,OAAO;YACL;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;oCAAgB,MAAM;;;;;;8CACxC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAEtD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,OAAO,KAAK;oCACnB,UAAU,CAAC,IAAM,UAAU;4CAAE,GAAG,MAAM;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC9D,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,OAAO,KAAK;oCACnB,UAAU,CAAC,IAAM,UAAU;4CAAE,GAAG,MAAM;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC9D,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,OAAO,IAAI;oCAClB,UAAU,CAAC,IAAM,UAAU;4CAAE,GAAG,MAAM;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC7D,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;wBAM3C,kCACC,8OAAC;4BAAI,WAAW,CAAC,uCAAuC,EACtD,iBAAiB,OAAO,GACpB,uDACA,gDACJ;;gCACC,iBAAiB,OAAO,iBACvB,8OAAC,2NAAA,CAAA,cAAW;oCAAC,MAAM;;;;;yDAEnB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;8CAErB,8OAAC;oCAAK,WAAU;8CAAW,iBAAiB,OAAO;;;;;;;;;;;;sCAKvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAMV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,UAAU,gBAAgB,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,IAAI;4BACxE,WAAU;sCAET,eAAe,WAAW;;;;;;sCAE7B,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,kBAAkB;4BAC7B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///root/Github/webMD_write/webmd-writer/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Settings, Send, FileText, PenTool, Github, AlertCircle } from 'lucide-react';\nimport MarkdownEditor from '@/components/MarkdownEditor';\nimport ConfigModal from '@/components/ConfigModal';\nimport { GitHubConfig, GitHubService } from '@/lib/github';\n\ntype ContentType = 'blog' | 'essay';\n\ninterface Article {\n  title: string;\n  content: string;\n  type: ContentType;\n  tags: string[];\n}\n\nexport default function Home() {\n  const [article, setArticle] = useState<Article>({\n    title: '',\n    content: '',\n    type: 'blog',\n    tags: []\n  });\n  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);\n  const [githubConfig, setGithubConfig] = useState<GitHubConfig | null>(null);\n  const [isPublishing, setIsPublishing] = useState(false);\n  const [publishResult, setPublishResult] = useState<{\n    success: boolean;\n    message: string;\n  } | null>(null);\n  const [tagInput, setTagInput] = useState('');\n\n  // 从localStorage加载配置\n  useEffect(() => {\n    const savedConfig = localStorage.getItem('github-config');\n    if (savedConfig) {\n      try {\n        setGithubConfig(JSON.parse(savedConfig));\n      } catch (error) {\n        console.error('Failed to parse saved config:', error);\n      }\n    }\n  }, []);\n\n  const handleConfigSave = (config: GitHubConfig) => {\n    setGithubConfig(config);\n    localStorage.setItem('github-config', JSON.stringify(config));\n  };\n\n  const handleAddTag = () => {\n    if (tagInput.trim() && !article.tags.includes(tagInput.trim())) {\n      setArticle({\n        ...article,\n        tags: [...article.tags, tagInput.trim()]\n      });\n      setTagInput('');\n    }\n  };\n\n  const handleRemoveTag = (tagToRemove: string) => {\n    setArticle({\n      ...article,\n      tags: article.tags.filter(tag => tag !== tagToRemove)\n    });\n  };\n\n  const handlePublish = async () => {\n    if (!githubConfig) {\n      setIsConfigModalOpen(true);\n      return;\n    }\n\n    if (!article.title.trim() || !article.content.trim()) {\n      setPublishResult({\n        success: false,\n        message: '请填写标题和内容'\n      });\n      return;\n    }\n\n    setIsPublishing(true);\n    setPublishResult(null);\n\n    try {\n      const githubService = new GitHubService(githubConfig);\n\n      // 生成文件路径和内容\n      const filePath = GitHubService.generateFilePath(article.type, article.title);\n      const frontmatter = GitHubService.generateFrontmatter(\n        article.title,\n        article.type,\n        article.tags\n      );\n      const fullContent = frontmatter + article.content;\n\n      // 上传到GitHub\n      await githubService.createOrUpdateFile({\n        path: filePath,\n        content: fullContent,\n        message: `Add ${article.type}: ${article.title}`\n      });\n\n      setPublishResult({\n        success: true,\n        message: `${article.type === 'blog' ? '博客' : '随笔'}发布成功！`\n      });\n\n      // 清空表单\n      setArticle({\n        title: '',\n        content: '',\n        type: 'blog',\n        tags: []\n      });\n\n    } catch (error) {\n      setPublishResult({\n        success: false,\n        message: `发布失败: ${error}`\n      });\n    } finally {\n      setIsPublishing(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 头部 */}\n      <header className=\"bg-white border-b border-gray-200 px-4 py-3\">\n        <div className=\"max-w-6xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center gap-2\">\n              <PenTool className=\"text-blue-600\" size={24} />\n              <h1 className=\"text-xl font-bold text-gray-900\">WebMD Writer</h1>\n            </div>\n            <div className=\"hidden sm:block text-sm text-gray-500\">\n              博客写作与发布工具\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-3\">\n            {githubConfig && (\n              <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n                <Github size={16} />\n                <span className=\"hidden sm:inline\">\n                  {githubConfig.owner}/{githubConfig.repo}\n                </span>\n              </div>\n            )}\n            <button\n              onClick={() => setIsConfigModalOpen(true)}\n              className=\"flex items-center gap-2 px-3 py-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\"\n              title=\"GitHub 配置\"\n            >\n              <Settings size={16} />\n              <span className=\"hidden sm:inline\">配置</span>\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-6xl mx-auto p-4 space-y-6\">\n        {/* 文章信息 */}\n        <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* 标题 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                标题 *\n              </label>\n              <input\n                type=\"text\"\n                value={article.title}\n                onChange={(e) => setArticle({ ...article, title: e.target.value })}\n                placeholder=\"输入文章标题...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            {/* 类型选择 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                类型\n              </label>\n              <div className=\"flex gap-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"blog\"\n                    checked={article.type === 'blog'}\n                    onChange={(e) => setArticle({ ...article, type: e.target.value as ContentType })}\n                    className=\"mr-2\"\n                  />\n                  <FileText size={16} className=\"mr-1\" />\n                  博客\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"essay\"\n                    checked={article.type === 'essay'}\n                    onChange={(e) => setArticle({ ...article, type: e.target.value as ContentType })}\n                    className=\"mr-2\"\n                  />\n                  <PenTool size={16} className=\"mr-1\" />\n                  随笔\n                </label>\n              </div>\n            </div>\n          </div>\n\n          {/* 标签 */}\n          <div className=\"mt-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              标签\n            </label>\n            <div className=\"flex flex-wrap gap-2 mb-3\">\n              {article.tags.map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-md\"\n                >\n                  {tag}\n                  <button\n                    onClick={() => handleRemoveTag(tag)}\n                    className=\"text-blue-600 hover:text-blue-800\"\n                  >\n                    ×\n                  </button>\n                </span>\n              ))}\n            </div>\n            <div className=\"flex gap-2\">\n              <input\n                type=\"text\"\n                value={tagInput}\n                onChange={(e) => setTagInput(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}\n                placeholder=\"添加标签...\"\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <button\n                onClick={handleAddTag}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                添加\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Markdown 编辑器 */}\n        <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n          <MarkdownEditor\n            value={article.content}\n            onChange={(content) => setArticle({ ...article, content })}\n            height=\"600px\"\n          />\n        </div>\n\n        {/* 发布结果 */}\n        {publishResult && (\n          <div className={`flex items-center gap-2 p-4 rounded-lg ${\n            publishResult.success\n              ? 'bg-green-50 text-green-700 border border-green-200'\n              : 'bg-red-50 text-red-700 border border-red-200'\n          }`}>\n            <AlertCircle size={16} />\n            <span>{publishResult.message}</span>\n          </div>\n        )}\n\n        {/* 发布按钮 */}\n        <div className=\"flex justify-end\">\n          <button\n            onClick={handlePublish}\n            disabled={isPublishing || !article.title.trim() || !article.content.trim()}\n            className=\"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Send size={16} />\n            {isPublishing ? '发布中...' : '发布到 GitHub'}\n          </button>\n        </div>\n      </main>\n\n      {/* 配置模态框 */}\n      <ConfigModal\n        isOpen={isConfigModalOpen}\n        onClose={() => setIsConfigModalOpen(false)}\n        onConfigSave={handleConfigSave}\n        currentConfig={githubConfig || undefined}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;QAC9C,OAAO;QACP,SAAS;QACT,MAAM;QACN,MAAM,EAAE;IACV;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGvC;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,aAAa;YACf,IAAI;gBACF,gBAAgB,KAAK,KAAK,CAAC;YAC7B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK;YAC9D,WAAW;gBACT,GAAG,OAAO;gBACV,MAAM;uBAAI,QAAQ,IAAI;oBAAE,SAAS,IAAI;iBAAG;YAC1C;YACA,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,WAAW;YACT,GAAG,OAAO;YACV,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;QAC3C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc;YACjB,qBAAqB;YACrB;QACF;QAEA,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,OAAO,CAAC,IAAI,IAAI;YACpD,iBAAiB;gBACf,SAAS;gBACT,SAAS;YACX;YACA;QACF;QAEA,gBAAgB;QAChB,iBAAiB;QAEjB,IAAI;YACF,MAAM,gBAAgB,IAAI,oHAAA,CAAA,gBAAa,CAAC;YAExC,YAAY;YACZ,MAAM,WAAW,oHAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC,QAAQ,IAAI,EAAE,QAAQ,KAAK;YAC3E,MAAM,cAAc,oHAAA,CAAA,gBAAa,CAAC,mBAAmB,CACnD,QAAQ,KAAK,EACb,QAAQ,IAAI,EACZ,QAAQ,IAAI;YAEd,MAAM,cAAc,cAAc,QAAQ,OAAO;YAEjD,YAAY;YACZ,MAAM,cAAc,kBAAkB,CAAC;gBACrC,MAAM;gBACN,SAAS;gBACT,SAAS,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,KAAK,EAAE;YAClD;YAEA,iBAAiB;gBACf,SAAS;gBACT,SAAS,GAAG,QAAQ,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,CAAC;YAC1D;YAEA,OAAO;YACP,WAAW;gBACT,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,MAAM,EAAE;YACV;QAEF,EAAE,OAAO,OAAO;YACd,iBAAiB;gBACf,SAAS;gBACT,SAAS,CAAC,MAAM,EAAE,OAAO;YAC3B;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;4CAAgB,MAAM;;;;;;sDACzC,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;;8CAElD,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;gCACZ,8BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;sDACd,8OAAC;4CAAK,WAAU;;gDACb,aAAa,KAAK;gDAAC;gDAAE,aAAa,IAAI;;;;;;;;;;;;;8CAI7C,8OAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;oCACV,OAAM;;sDAEN,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,OAAM;gEACN,SAAS,QAAQ,IAAI,KAAK;gEAC1B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAgB;gEAC9E,WAAU;;;;;;0EAEZ,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAS;;;;;;;kEAGzC,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,OAAM;gEACN,SAAS,QAAQ,IAAI,KAAK;gEAC1B,UAAU,CAAC,IAAM,WAAW;wEAAE,GAAG,OAAO;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAgB;gEAC9E,WAAU;;;;;;0EAEZ,8OAAC,4MAAA,CAAA,UAAO;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAS;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;gDAEC,WAAU;;oDAET;kEACD,8OAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;kEACX;;;;;;;+CAPI;;;;;;;;;;kDAaX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;gDACxC,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;4BACb,OAAO,QAAQ,OAAO;4BACtB,UAAU,CAAC,UAAY,WAAW;oCAAE,GAAG,OAAO;oCAAE;gCAAQ;4BACxD,QAAO;;;;;;;;;;;oBAKV,+BACC,8OAAC;wBAAI,WAAW,CAAC,uCAAuC,EACtD,cAAc,OAAO,GACjB,uDACA,gDACJ;;0CACA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;;;;;;0CACnB,8OAAC;0CAAM,cAAc,OAAO;;;;;;;;;;;;kCAKhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU,gBAAgB,CAAC,QAAQ,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,OAAO,CAAC,IAAI;4BACxE,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;gCACX,eAAe,WAAW;;;;;;;;;;;;;;;;;;0BAMjC,8OAAC,iIAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,cAAc;gBACd,eAAe,gBAAgB;;;;;;;;;;;;AAIvC", "debugId": null}}]}