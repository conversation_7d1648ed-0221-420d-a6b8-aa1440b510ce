# WebMD Writer 使用演示

## 应用概览

WebMD Writer 是一个现代化的博客写作工具，让你可以：
- 在浏览器中直接撰写 Markdown 文章
- 实时预览文章效果
- 一键发布到 GitHub 仓库
- 自动触发博客网站重新部署

## 使用步骤

### 第一步：配置 GitHub

1. 点击右上角的"配置"按钮
2. 填写 GitHub 配置信息：
   - **Personal Access Token**: 从 GitHub 获取的访问令牌
   - **仓库所有者**: 你的 GitHub 用户名（如：`username`）
   - **仓库名称**: 博客仓库名（如：`my-blog`）

### 第二步：撰写文章

1. **填写基本信息**：
   - 输入文章标题
   - 选择文章类型（博客 or 随笔）
   - 添加标签（可选）

2. **编写内容**：
   - 在编辑器中使用 Markdown 语法撰写
   - 支持代码块、表格、列表等
   - 右侧实时预览效果

3. **预览模式**：
   - 编辑模式：专注写作
   - 预览模式：查看最终效果
   - 分屏模式：同时编辑和预览

### 第三步：发布文章

1. 确认文章内容无误
2. 点击"发布到 GitHub"按钮
3. 等待发布完成提示
4. 文章自动保存到 GitHub 仓库

## 文件结构说明

发布后的文件会按以下结构保存：

```
你的博客仓库/
└── src/content/
    ├── blog/                    # 博客文章目录
    │   └── 2024-08-01-my-first-blog.md
    └── essays/                  # 随笔文章目录
        └── 2024-08-01-my-thoughts.md
```

## Markdown 语法示例

### 标题
```markdown
# 一级标题
## 二级标题
### 三级标题
```

### 文本格式
```markdown
**粗体文本**
*斜体文本*
~~删除线~~
`行内代码`
```

### 代码块
```markdown
\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`
```

### 列表
```markdown
- 无序列表项 1
- 无序列表项 2

1. 有序列表项 1
2. 有序列表项 2
```

### 链接和图片
```markdown
[链接文本](https://example.com)
![图片描述](https://example.com/image.jpg)
```

### 表格
```markdown
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
```

### 引用
```markdown
> 这是一个引用块
> 可以包含多行内容
```

## 自动生成的 Frontmatter

每篇文章都会自动添加元数据：

```yaml
---
title: "文章标题"
date: 2024-08-01T10:30:00.000Z
type: blog
tags: ["技术", "教程"]
draft: false
---
```

## 常见问题

### Q: GitHub Token 如何获取？
A: 
1. 访问 GitHub Settings → Developer settings → Personal access tokens
2. 点击 "Generate new token (classic)"
3. 勾选 "repo" 权限
4. 复制生成的 token

### Q: 发布失败怎么办？
A: 检查以下几点：
- GitHub Token 是否有效
- 仓库名称是否正确
- 是否有仓库写入权限
- 网络连接是否正常

### Q: 可以修改文件保存路径吗？
A: 可以，在 `src/lib/github.ts` 中修改 `generateFilePath` 函数

### Q: 支持图片上传吗？
A: 当前版本需要手动上传图片到图床或仓库，然后在文章中引用链接

## 技术特性

- **实时预览**: 编辑时即时看到渲染效果
- **语法高亮**: 代码块自动高亮显示
- **响应式设计**: 支持桌面和移动设备
- **本地存储**: GitHub 配置安全保存在本地
- **错误处理**: 友好的错误提示和处理

## 下一步计划

- [ ] 支持图片拖拽上传
- [ ] 添加文章草稿功能
- [ ] 支持文章编辑和删除
- [ ] 添加文章列表管理
- [ ] 支持多种主题样式
- [ ] 添加文章搜索功能

---

开始使用 WebMD Writer，让博客写作变得更加简单高效！
