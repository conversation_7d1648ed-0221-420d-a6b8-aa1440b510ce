# WebMD Writer

一个基于 Next.js 的博客写作与发布工具，可以直接将 Markdown 文章发布到 GitHub 仓库。

## 功能特性

- 📝 **Markdown 编辑器**: 支持实时预览、语法高亮
- 🔄 **分屏模式**: 编辑、预览、分屏三种视图模式
- 📂 **文章分类**: 支持博客和随笔两种类型
- 🏷️ **标签管理**: 为文章添加和管理标签
- 🚀 **一键发布**: 直接发布到 GitHub 仓库
- ⚙️ **配置管理**: 安全的 GitHub 配置管理

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 配置 GitHub

1. 点击右上角的"配置"按钮
2. 填写以下信息：
   - **GitHub Personal Access Token**: 需要 `repo` 权限
   - **仓库所有者**: 你的 GitHub 用户名或组织名
   - **仓库名称**: 博客仓库的名称

### 4. 获取 GitHub Token

1. 访问 [GitHub Settings → Developer settings → Personal access tokens](https://github.com/settings/tokens)
2. 点击 "Generate new token (classic)"
3. 选择 "repo" 权限
4. 复制生成的 token

## 使用说明

### 写作流程

1. **填写文章信息**
   - 输入文章标题
   - 选择文章类型（博客/随笔）
   - 添加标签（可选）

2. **编写内容**
   - 在 Markdown 编辑器中撰写内容
   - 支持实时预览
   - 支持代码高亮

3. **发布文章**
   - 点击"发布到 GitHub"按钮
   - 文章将自动上传到指定的 GitHub 仓库

### 文件组织

发布的文章将按以下结构组织：

```
src/content/
├── blog/           # 博客文章
│   └── 2024-01-01-article-title.md
└── essays/         # 随笔文章
    └── 2024-01-01-essay-title.md
```

### Frontmatter 格式

每篇文章都会自动添加 frontmatter：

```yaml
---
title: "文章标题"
date: 2024-01-01T12:00:00.000Z
type: blog
tags: ["标签1", "标签2"]
draft: false
---
```

## 技术栈

- **前端框架**: Next.js 15
- **样式**: Tailwind CSS
- **编辑器**: Monaco Editor
- **Markdown 渲染**: react-markdown
- **GitHub API**: @octokit/rest
- **图标**: Lucide React

## 项目结构

```
webmd-writer/
├── src/
│   ├── app/                 # Next.js App Router
│   ├── components/          # React 组件
│   │   ├── MarkdownEditor.tsx
│   │   └── ConfigModal.tsx
│   └── lib/                 # 工具库
│       └── github.ts        # GitHub API 服务
├── public/                  # 静态资源
└── package.json
```

## 开发

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

### 环境要求

- Node.js 18+
- npm 或 yarn

## 部署

### Vercel 部署

1. 将项目推送到 GitHub
2. 在 Vercel 中导入项目
3. 自动部署完成

### 其他平台

项目是标准的 Next.js 应用，可以部署到任何支持 Node.js 的平台。

## 注意事项

1. **GitHub Token 安全**: Token 存储在浏览器的 localStorage 中，请确保在安全的环境中使用
2. **仓库权限**: 确保 GitHub Token 有对目标仓库的写入权限
3. **文件路径**: 默认文件路径为 `src/content/blog/` 和 `src/content/essays/`，可根据需要修改

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
